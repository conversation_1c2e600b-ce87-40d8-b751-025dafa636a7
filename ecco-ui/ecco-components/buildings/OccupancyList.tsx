import * as React from "react";
import {FC, useMemo, useState} from "react";
import {
    CircularProgress,
    Box,
    Typography,
    Card,
    CardContent,
    List,
    ListItem,
    ListItemText,
    Divider,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Grid,
    TablePagination
} from "@eccosolutions/ecco-mui";
import {EccoDate} from "@eccosolutions/ecco-common";
import {useBuildingsWithOccupancyHistory} from "../data/entityLoadHooks";
import {Building, fullAddress, OccupancyFilter, ServiceRecipient} from "ecco-dto";
import {link} from "../MUIComponentUtils";
import {useDebounce} from "../hooks";
import {SearchFilter} from "./SearchFilter";

interface OccupancyRecord {
    id: number;
    serviceRecipient?: ServiceRecipient;
    serviceRecipientId: number;
    validFrom: string;
    validTo?: string;
}


interface OccupancyHistorySectionProps {
    occupancyHistory?: Array<OccupancyRecord>;
    applicationRootPath: string;
    occupancyFilter?: OccupancyFilter;
    search: string;
}

/**
 * Helper function to format building display name with address and parent building
 */
const BuildingName: React.FC<{ building: Building }> = ({ building }) => {
    return (<>
        {building.name || `[b-id ${building.buildingId}]`}
        {building.parentName && <span style={{fontSize: 'small', fontWeight: 'bold'}}> {building.parentName}</span>}
        <Typography style={{fontSize: 'small'}}>{fullAddress(building.address)}</Typography>
        <Typography style={{fontSize: 'small'}}>{`[b-id ${building.buildingId}] [a-id ${building.locationId}]`}</Typography>
    </>);
}


/**
 * Component for rendering a single occupancy record
 */
const OccupancyItem: React.FC<{occupancy: OccupancyRecord; applicationRootPath: string}> = ({
    occupancy,
    applicationRootPath
}) => {
    const srUrl = new URL(
        `${applicationRootPath}nav/r/main/sr2/${occupancy.serviceRecipientId}/`,
        location.href
    ).href;

    const Name = (
        <Typography
            variant="subtitle2"
            component="h3"
            gutterBottom
            style={{color: "success.main" || "inherit", fontWeight: "bold"}}
        >
            {occupancy.serviceRecipientId
                ? link(`${occupancy.serviceRecipient?.displayName}`, () =>
                      window.open(srUrl, "_blank")
                  )
                : "VOID"}
        </Typography>
    );

    const When = (
        <Typography component="span" variant="body2">
            from {new Date(occupancy.validFrom).toLocaleDateString()}
            {occupancy.validTo && <> to {new Date(occupancy.validTo).toLocaleDateString()}</>}
        </Typography>
    );

    return (
        <>
            <ListItem>
                <ListItemText primary={Name} secondary={When} />
            </ListItem>
        </>
    );
};


/**
 * Component for displaying current occupancy for a building
 */
const CurrentOccupancySection: React.FC<OccupancyHistorySectionProps> = ({
    occupancyHistory,
    applicationRootPath,
    occupancyFilter = "all",
    search: string
}) => {
    if (!occupancyHistory || occupancyHistory.length === 0) {
        return (
            <Typography variant="body2" color="textSecondary">
                -
            </Typography>
        );
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const occupancies = occupancyHistory.filter(occupancy => {
        const validFrom = new Date(occupancy.validFrom);
        const validTo = occupancy.validTo ? new Date(occupancy.validTo) : null;
        // is in current period
        return validFrom <= today && (!validTo || validTo >= today);
    });

    if (occupancies.length === 0) {
        return null;
    }

    return (
        <Box mb={2}>
            <List dense>
                {occupancies.map((occupancy, index) => (
                    <React.Fragment key={occupancy.id}>
                        <OccupancyItem
                            occupancy={occupancy}
                            applicationRootPath={applicationRootPath}
                        />
                        {index < occupancies.length - 1 && <Divider />}
                    </React.Fragment>
                ))}
            </List>
        </Box>
    );
};

/**
 * Component for displaying past and future occupancy in an accordion
 */
const OccupancyHistoryAccordion: React.FC<OccupancyHistorySectionProps> = ({
    occupancyHistory,
    applicationRootPath,
    occupancyFilter = "all",
    search
}) => {
    if (!occupancyHistory || occupancyHistory.length === 0) {
        return null;
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Categorize occupancy records
    const pastOccupancies = occupancyHistory.filter(occupancy => {
        const validTo = occupancy.validTo ? new Date(occupancy.validTo) : null;
        // is past
        return validTo && validTo < today;
    });

    const futureOccupancies = occupancyHistory.filter(occupancy => {
        const validFrom = new Date(occupancy.validFrom);

        // is future
        return validFrom > today;
    });

    // Don't show accordion if no past or future occupancies
    if (pastOccupancies.length === 0 && futureOccupancies.length === 0) {
        return null;
    }

    return (
        <Box mt={0}>
            <Accordion>
                <AccordionSummary>
                    <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" style={{fontWeight: "bold"}}>
                                {pastOccupancies.length > 0 && `${pastOccupancies.length} past `}
                            </Typography>
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" style={{fontWeight: "bold"}}>
                                {futureOccupancies.length > 0 &&
                                    `${futureOccupancies.length} future`}
                            </Typography>
                        </Grid>
                    </Grid>
                </AccordionSummary>
                <AccordionDetails>
                    <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                            {pastOccupancies.length > 0 && (
                                <List dense>
                                    {pastOccupancies.map((occupancy, index) => (
                                        <React.Fragment key={occupancy.id}>
                                            <OccupancyItem
                                                occupancy={occupancy}
                                                applicationRootPath={applicationRootPath}
                                            />
                                            {/*{index < pastOccupancies.length - 1 && <Divider />}*/}
                                        </React.Fragment>
                                    ))}
                                </List>
                            )}
                        </Grid>
                        <Grid item xs={12} md={6}>
                            {futureOccupancies.length > 0 && (
                                <List dense>
                                    {futureOccupancies.map((occupancy, index) => (
                                        <React.Fragment key={occupancy.id}>
                                            <OccupancyItem
                                                occupancy={occupancy}
                                                applicationRootPath={applicationRootPath}
                                            />
                                            {/*{index < futureOccupancies.length - 1 && <Divider />}*/}
                                        </React.Fragment>
                                    ))}
                                </List>
                            )}
                        </Grid>
                    </Grid>
                </AccordionDetails>
            </Accordion>
        </Box>
    );
};

/**
 * Component for displaying occupancy lists
 * Use with: const {applicationRootPath} = window.applicationProperties;
 */
export const OccupancyList: React.FC<{
    applicationRootPath: string;
}> = ({applicationRootPath}) => {
    const [pageNumber, setPageNumber] = useState<number>(0);
    const [from, setFrom] = useState<EccoDate>(EccoDate.todayLocalTime().subtractMonths(12));
    const [to, setTo] = useState<EccoDate>(EccoDate.todayLocalTime().addMonths(3));
    const [occupancyFilter, setOccupancyFilter] = useState<OccupancyFilter>("all");

    // Separate states for input value and debounced search
    const [searchInput, setSearchInput] = useState<string>("");
    const debouncedSearch = useDebounce(searchInput, 500);

    //useAppBarOptions(""); // not needed with proper AppBarBase defaultAppBar

    const pageSize = 20; // Match the buildingsSize from the hook

    const handleChangePage = (event: unknown, newPage: number) => {
        setPageNumber(newPage);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
        // For now, we keep the page size fixed at 20 as per the hook implementation
        // This handler is required by TablePagination but we don't use it
    };

    const {bldgsWithOccupancy, totalCount, error, loading} = useBuildingsWithOccupancyHistory(
        from,
        to,
        occupancyFilter,
        debouncedSearch,
        pageNumber
    );

    const bldgs = bldgsWithOccupancy || [];

    return (
        <>
            <OccupancyListDisplay
                applicationRootPath={applicationRootPath}
                loading={loading}
                error={error}
                bldgs={bldgs}
                occupancyFilter={occupancyFilter}
                setOccupancyFilter={setOccupancyFilter}
                debouncedSearch={debouncedSearch}
                searchInput={searchInput}
                setSearchInput={setSearchInput}
            />
            <Pagination
                searchInProgress={debouncedSearch.trim() === ""}
                totalCount={totalCount}
                pageNumber={pageNumber}
                pageSize={pageSize}
                handleChangePage={handleChangePage}
                handleChangeRowsPerPage={handleChangeRowsPerPage}
            />
        </>
    );
};

const Pagination: React.FC<{
    searchInProgress: boolean;
    totalCount: number;
    pageNumber: number;
    pageSize: number;
    handleChangePage: (event: unknown, newPage: number) => void;
    handleChangeRowsPerPage: (event: React.ChangeEvent<HTMLInputElement>) => void;
}> = ({
    searchInProgress,
    totalCount,
    pageNumber,
    pageSize,
    handleChangePage,
    handleChangeRowsPerPage
}) => {
    return (
        <>
            {/* Only show pagination when not searching */}
            {!searchInProgress && (
                <Box display="flex" justifyContent="center" mt={2}>
                    <TablePagination
                        rowsPerPageOptions={[25]}
                        component="div"
                        count={totalCount}
                        rowsPerPage={pageSize}
                        page={pageNumber}
                        backIconButtonProps={{
                            "aria-label": "Previous Page"
                        }}
                        nextIconButtonProps={{
                            "aria-label": "Next Page"
                        }}
                        onChangePage={handleChangePage}
                        onChangeRowsPerPage={handleChangeRowsPerPage}
                    />
                </Box>
            )}
        </>
    );
};

const OccupancyListDisplay: React.FC<{
    applicationRootPath: string;
    loading: boolean;
    error: any;
    bldgs: Building[];
    occupancyFilter: OccupancyFilter;
    setOccupancyFilter: (occupancyFilter: OccupancyFilter) => void;
    debouncedSearch: string;
    searchInput: string;
    setSearchInput: (searchInput: string) => void;
}> = ({
    applicationRootPath,
    loading,
    error,
    bldgs,
    occupancyFilter,
    setOccupancyFilter,
    searchInput,
    setSearchInput,
    debouncedSearch
}) => {
    const LoadingDisplay = loading && (
        <Box display="flex" justifyContent="center" p={2}>
            <CircularProgress />
        </Box>
    );

    const ErrorDisplay = error && (
        <Box p={2}>
            <Typography color="error">error loading data: {error.message}</Typography>
        </Box>
    );

    return (
        <Grid container justify="center">
            <Grid item xs={12} md={8}>
                <Box p={2} pb={1}>
                    <SearchFilter
                        searchInput={searchInput}
                        onSearchInputChange={setSearchInput}
                        occupancyFilter={occupancyFilter}
                        onOccupancyFilterChange={setOccupancyFilter}
                    />
                </Box>

                {LoadingDisplay}
                {ErrorDisplay}

                {!loading && !error && (
                    <>
                        <Box p={2} pt={0}>
                            {debouncedSearch.trim() !== "" && (
                                <Typography variant="body2" color="textSecondary" gutterBottom>
                                    Showing {bldgs.length} of {bldgs.length} buildings
                                </Typography>
                            )}
                            {bldgs.length === 0 && debouncedSearch.trim() !== "" ? (
                                <Typography
                                    variant="body1"
                                    color="textSecondary"
                                    style={{textAlign: "center", padding: "2rem"}}
                                >
                                    No buildings found matching "{debouncedSearch}"
                                </Typography>
                            ) : bldgs.length === 0 ? (
                                <Box p={2}>
                                    <Typography>no units found</Typography>
                                </Box>
                            ) : (
                                bldgs.map(building => (
                                    <Box key={building.buildingId} mb={2}>
                                        <Card>
                                            <CardContent>
                                                <Typography
                                                    variant="h6"
                                                    component="h2"
                                                    gutterBottom
                                                >
                                                    <BuildingName building={building} />
                                                </Typography>

                                                <CurrentOccupancySection
                                                    occupancyHistory={building.occupancyHistory}
                                                    applicationRootPath={applicationRootPath}
                                                    occupancyFilter={occupancyFilter}
                                                    search={debouncedSearch}
                                                />
                                            </CardContent>
                                        </Card>
                                        <OccupancyHistoryAccordion
                                            occupancyHistory={building.occupancyHistory}
                                            applicationRootPath={applicationRootPath}
                                            occupancyFilter={occupancyFilter}
                                            search=""
                                        />
                                    </Box>
                                ))
                            )}
                        </Box>
                    </>
                )}
            </Grid>
        </Grid>
    );
};