import * as React from "react";
import {
    Typography,
    TextField,
    InputAdornment,
    RadioGroup,
    FormControlLabel,
    Radio
} from "@eccosolutions/ecco-mui";
import SearchIcon from "@material-ui/icons/Search";
import {OccupancyFilter} from "ecco-dto";

interface SearchFilterProps {
    searchInput: string;
    onSearchInputChange: (value: string) => void;
    occupancyFilter: OccupancyFilter;
    onOccupancyFilterChange: (filter: OccupancyFilter) => void;
}

/**
 * Component for filtering buildings by search text and occupancy status
 */
export const SearchFilter: React.FC<SearchFilterProps> = ({
    searchInput,
    onSearchInputChange,
    occupancyFilter,
    onOccupancyFilterChange
}) => {
    return (
        <>
            <Typography variant="subtitle2" gutterBottom>
                Search buildings:
            </Typography>
            <TextField
                fullWidth
                variant="outlined"
                placeholder="Search by building name, ID, or address..."
                value={searchInput}
                onChange={event => onSearchInputChange(event.target.value)}
                InputProps={{
                    startAdornment: (
                        <InputAdornment position="start">
                            <SearchIcon />
                        </InputAdornment>
                    )
                }}
                style={{marginBottom: 16}}
            />
            <Typography variant="subtitle2" gutterBottom>
                Show occupancies:
            </Typography>
            <RadioGroup
                row
                value={occupancyFilter}
                onChange={event => onOccupancyFilterChange(event.target.value as OccupancyFilter)}
                name="occupancyFilter"
            >
                <FormControlLabel value="all" control={<Radio />} label="All" />
                <FormControlLabel value="occupied" control={<Radio />} label="Occupied only" />
                <FormControlLabel value="void" control={<Radio />} label="Void only" />
            </RadioGroup>
        </>
    );
};
